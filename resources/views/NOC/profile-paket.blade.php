@extends('layouts.contentNavbarLayout')

@section('title', 'Profile Paket')

@section('content')
<div class="row">
    <div class="col-12">
        <!-- Header Card -->
        <div class="card mb-3">
            <div class="card-header">
                <h5 class="card-title fw-bold">Profile Paket</h5>
                <small class="card-subtitle">Daftar Profile Paket dan <PERSON></small>
            </div>
        </div>

        <!-- Search and Filter Card -->
        <div class="card mb-3">
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label">Pencarian Paket</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="bx bx-search"></i></span>
                            <input type="text" class="form-control" id="search-input" placeholder="Cari nama paket..." value="">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Harga Minimum</label>
                        <div class="input-group">
                            <span class="input-group-text">Rp</span>
                            <input type="number" class="form-control" id="min-price" placeholder="0" min="0">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Harga Maksimum</label>
                        <div class="input-group">
                            <span class="input-group-text">Rp</span>
                            <input type="number" class="form-control" id="max-price" placeholder="1000000" min="0">
                        </div>
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="button" class="btn btn-outline-secondary w-100" id="reset-filters">
                            <i class="bx bx-refresh me-1"></i>Reset
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Data Table Card -->
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-start mb-3">
                    <button type="button" class="btn btn-outline-warning btn-sm" data-bs-toggle="modal" data-bs-target="#modalTambahPaket">
                        <i class="bx bx-plus me-1"></i>Tambah Paket
                    </button>
                </div>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-dark">
                            <tr class="text-center">
                                <th>No</th>
                                <th>Nama Profile</th>
                                <th>Harga</th>
                                <th>Jumlah Pelanggan</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody class="text-center" id="paket-table-body">
                            @forelse ($paket as $item)
                            <tr>
                                <td>{{ $loop->iteration + ($paket->currentPage() - 1) * $paket->perPage() }}</td>
                                <td>
                                    <span class="badge {{ $item->nama_paket == 'ISOLIREBILLING' ? 'bg-danger text-danger' : 'bg-info text-primary' }} bg-opacity-10">
                                        {{ $item->nama_paket ?? '' }}
                                    </span>
                                </td>
                                <td>Rp {{number_format((int)$item->harga ?? 0, 0, ',', '.')}}</td>
                                <td>
                                    <span class="fw-bold badge bg-warning bg-opacity-10 text-warning">
                                        {{ $item->customer->count() ?? 0 }}
                                    </span>
                                </td>
                                <td>
                                    <a href="" data-bs-toggle="tooltip" title="Edit Profile" data-bs-placement="bottom">
                                        <i class="bx bx-edit text-warning"></i>
                                    </a>|
                                    <a href="/hapus/paket/{{ $item->id }}" data-bs-toggle="tooltip" title="Hapus Profile" data-bs-placement="bottom" onclick="return confirm('Apakah Anda yakin ingin menghapus paket ini?')">
                                        <i class="bx bx-trash text-danger"></i>
                                    </a>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="5" class="text-center py-4">
                                    <div class="d-flex flex-column align-items-center">
                                        <i class="bx bx-search-alt-2 fs-1 text-muted mb-2"></i>
                                        <span class="text-muted">Tidak ada data paket yang ditemukan</span>
                                    </div>
                                </td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                @if($paket->hasPages())
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div class="text-muted">
                            Menampilkan {{ $paket->firstItem() ?? 0 }} sampai {{ $paket->lastItem() ?? 0 }}
                            dari {{ $paket->total() }} data
                        </div>
                        <div id="pagination-container">
                            {{ $paket->links() }}
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div id="loading-overlay" class="position-fixed top-0 start-0 w-100 h-100 d-none" style="background: rgba(0,0,0,0.1); z-index: 9999;">
    <div class="d-flex justify-content-center align-items-center h-100">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>
</div>

{{-- Modal Tambah Paket --}}
<div class="modal fade" id="modalTambahPaket" tabindex="-1" aria-labelledby="modalTambahPaketLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTambahPaketLabel"><i class="bx bx-plus me-1"></i>Tambah Paket Langganan</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <hr class="mb-0">
            <form action="/tambah/paket" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label mb-2">*Nama Paket</label>
                        <input type="text" class="form-control" id="nama_paket" name="nama_paket" required>
                        <span>
                            <small class="text-danger fw-bold">*Harus sesuai dengan nama profile paket di Mikrotik</small>
                        </span>
                    </div>
                    <div class="mb-3">
                        <label class="form-label mb-2">*Harga Paket Langganan</label>
                        <input type="text" class="form-control" id="harga" name="harga" required>
                        <input hidden type="text" class="form-control" id="hargaRaw" name="hargaRaw">
                    </div>
                </div>
                <div class="modal-footer gap-2">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">
                        <i class="bx bx-x me-1"></i>Batal
                    </button>
                    <button type="submit" class="btn btn-outline-warning btn-sm">
                        <i class="bx bx-file me-1"></i>Simpan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>


<script>
    const harga = document.getElementById('harga');
    const hargaRaw = document.getElementById('hargaRaw');

    harga.addEventListener('input', function(e) {
        let value = this.value.replace(/[^,\d]/g, '').toString();
        let cleanValue = value.replace(/[^0-9]/g, '');

        // Simpan angka mentah ke input hidden
        hargaRaw.value = cleanValue;

        // Format ke Rupiah
        let formatted = new Intl.NumberFormat('id-ID', {
            style: 'currency',
            currency: 'IDR',
            minimumFractionDigits: 0
        }).format(cleanValue);

        this.value = formatted;
    });

    // Filter functionality
    $(document).ready(function() {
        let searchTimeout;

        // Search functionality
        $('#search-input').on('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(function() {
                performFilter();
            }, 500);
        });

        // Price filter functionality
        $('#min-price, #max-price').on('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(function() {
                performFilter();
            }, 500);
        });

        // Reset filters
        $('#reset-filters').on('click', function() {
            $('#search-input').val('');
            $('#min-price').val('');
            $('#max-price').val('');
            performFilter();
        });

        // ESC key to reset filters
        $(document).on('keydown', function(e) {
            if (e.key === 'Escape') {
                $('#search-input').val('');
                $('#min-price').val('');
                $('#max-price').val('');
                performFilter();
            }
        });
    });

    function performFilter() {
        const searchTerm = $('#search-input').val();
        const minPrice = $('#min-price').val();
        const maxPrice = $('#max-price').val();

        // Show loading
        $('#loading-overlay').removeClass('d-none');

        $.ajax({
            url: '{{ route("profile-paket") }}',
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            data: {
                search: searchTerm,
                min_price: minPrice,
                max_price: maxPrice
            },
            success: function(response) {
                if (response.success) {
                    // Update table content
                    updateTableContent(response.data);

                    // Update pagination
                    updatePagination(response.data);
                }
            },
            error: function(xhr, status, error) {
                console.error('Filter error:', error);
                showToast('Terjadi kesalahan saat memfilter data', 'error');
            },
            complete: function() {
                // Hide loading
                $('#loading-overlay').addClass('d-none');
            }
        });
    }

    function updateTableContent(data) {
        let html = '';

        if (data.data && data.data.length > 0) {
            data.data.forEach(function(item, index) {
                const rowNumber = index + 1 + ((data.current_page - 1) * data.per_page);

                const badgeClass = item.nama_paket === 'ISOLIREBILLING' ? 'bg-danger text-danger' : 'bg-info text-primary';
                const formattedPrice = new Intl.NumberFormat('id-ID').format(item.harga || 0);

                html += `
                    <tr>
                        <td>${rowNumber}</td>
                        <td>
                            <span class="badge ${badgeClass} bg-opacity-10">
                                ${item.nama_paket || ''}
                            </span>
                        </td>
                        <td>Rp ${formattedPrice}</td>
                        <td>
                            <span class="fw-bold badge bg-warning bg-opacity-10 text-warning">
                                ${item.customer ? item.customer.length : 0}
                            </span>
                        </td>
                        <td>
                            <a href="" data-bs-toggle="tooltip" title="Edit Profile" data-bs-placement="bottom">
                                <i class="bx bx-edit text-warning"></i>
                            </a>|
                            <a href="/hapus/paket/${item.id}" data-bs-toggle="tooltip" title="Hapus Profile" data-bs-placement="bottom" onclick="return confirm('Apakah Anda yakin ingin menghapus paket ini?')">
                                <i class="bx bx-trash text-danger"></i>
                            </a>
                        </td>
                    </tr>
                `;
            });
        } else {
            html = `
                <tr>
                    <td colspan="5" class="text-center py-4">
                        <div class="d-flex flex-column align-items-center">
                            <i class="bx bx-search-alt-2 fs-1 text-muted mb-2"></i>
                            <span class="text-muted">Tidak ada data paket yang ditemukan</span>
                        </div>
                    </td>
                </tr>
            `;
        }

        $('#paket-table-body').html(html);

        // Reinitialize tooltips
        $('[data-bs-toggle="tooltip"]').tooltip();
    }



    function updatePagination(data) {
        // Simple pagination info update
        if (data.total > 0) {
            const firstItem = ((data.current_page - 1) * data.per_page) + 1;
            const lastItem = Math.min(data.current_page * data.per_page, data.total);
            $('.text-muted').first().text(`Menampilkan ${firstItem} sampai ${lastItem} dari ${data.total} data`);
        } else {
            $('.text-muted').first().text('Menampilkan 0 sampai 0 dari 0 data');
        }
    }

    function showToast(message, type = 'success') {
        // Simple toast notification
        const toastClass = type === 'success' ? 'bg-success' : 'bg-danger';
        const toast = $(`
            <div class="toast align-items-center text-white ${toastClass} border-0 position-fixed"
                 style="top: 20px; right: 20px; z-index: 9999;" role="alert">
                <div class="d-flex">
                    <div class="toast-body">${message}</div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `);

        $('body').append(toast);
        const bsToast = new bootstrap.Toast(toast[0]);
        bsToast.show();

        // Remove toast after it's hidden
        toast.on('hidden.bs.toast', function() {
            $(this).remove();
        });
    }
</script>

@endsection
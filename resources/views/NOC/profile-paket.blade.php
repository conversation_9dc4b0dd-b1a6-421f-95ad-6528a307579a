@extends('layouts.contentNavbarLayout')

@section('title', 'Profile Paket')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card mb-3">
            <div class="card-header">
                <h5 class="card-title fw-bold">Profile Paket</h5>
                <small class="card-subtitle">Daftar Profile Paket dan <PERSON> Nisca<PERSON></small>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-start mb-5">
                    <button type="button" class="btn btn-outline-warning btn-sm" data-bs-toggle="modal" data-bs-target="#modalTambahPaket">
                        <i class="bx bx-plus me-1"></i>Tambah Paket
                    </button>
                </div>
                <div class="table-responsive">
                    <table class="table table-hover text-center">
                        <thead class="table-dark text-center">
                            <th>No</th>
                            <th>Nama Profile</th>
                            <th><PERSON><PERSON></th>
                            <th><PERSON><PERSON><PERSON></th>
                            <th>Aksi</th>
                        </thead>
                        <tbody class="text-center">
                            @php
                                $no = 1;
                            @endphp
                            @forelse ($paket as $item)
                            <tr>
                                <td>{{$no++}}</td>
                                <td>
                                    <span class="badge {{ $item->nama_paket == 'ISOLIREBILLING' ? 'bg-danger text-danger' : 'bg-info text-primary' }} bg-opacity-10">
                                        {{ $item->nama_paket ?? '' }}
                                    </span>
                                </td>
                                <td>Rp {{number_format((int)$item->harga ?? 0, 0, ',', '.')}}</td>
                                <td>
                                    <span class="fw-bold badge bg-warning bg-opacity-10 text-warning">
                                        {{ $item->customer->count() ?? 0 }}
                                    </span>
                                </td>
                                <td>
                                    <a href="" data-bs-toggle="tooltip" title="Edit Profile" data-bs-placement="bottom">
                                        <i class="bx bx-edit text-warning"></i>
                                    </a>|
                                    <a href="/hapus/paket/{{ $item->id }}" data-bs-toggle="tooltip" title="Hapus Profile" data-bs-placement="bottom">
                                        <i class="bx bx-trash text-danger"></i>
                                    </a>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="5">Tidak ada data</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
            <hr>
            <div class="d-flex">
                <div class="mx-auto">
                    {{ $paket->links() }}
                </div>
            </div>
        </div>
    </div>
</div>

{{-- Modal Tambah Paket --}}
<div class="modal fade" id="modalTambahPaket" tabindex="-1" aria-labelledby="modalTambahPaketLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTambahPaketLabel"><i class="bx bx-plus me-1"></i>Tambah Paket Langganan</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <hr class="mb-0">
            <form action="/tambah/paket" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label mb-2">*Nama Paket</label>
                        <input type="text" class="form-control" id="nama_paket" name="nama_paket" required>
                        <span>
                            <small class="text-danger fw-bold">*Harus sesuai dengan nama profile paket di Mikrotik</small>
                        </span>
                    </div>
                    <div class="mb-3">
                        <label class="form-label mb-2">*Harga Paket Langganan</label>
                        <input type="text" class="form-control" id="harga" name="harga" required>
                        <input hidden type="text" class="form-control" id="hargaRaw" name="hargaRaw">
                    </div>
                </div>
                <div class="modal-footer gap-2">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">
                        <i class="bx bx-x me-1"></i>Batal
                    </button>
                    <button type="submit" class="btn btn-outline-warning btn-sm">
                        <i class="bx bx-file me-1"></i>Simpan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>


<script>
    const harga = document.getElementById('harga');
    const hargaRaw = document.getElementById('hargaRaw');

    harga.addEventListener('input', function(e) {
        let value = this.value.replace(/[^,\d]/g, '').toString();
        let cleanValue = value.replace(/[^0-9]/g, '');

        // Simpan angka mentah ke input hidden
        hargaRaw.value = cleanValue;

        // Format ke Rupiah
        let formatted = new Intl.NumberFormat('id-ID', {
            style: 'currency',
            currency: 'IDR',
            minimumFractionDigits: 0
        }).format(cleanValue);

        this.value = formatted;
    });
</script>

@endsection